{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频分析详情 - 认知健康</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 40px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            color: #2d3748;
        }

        .back-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }

        .detail-grid {
            display: grid;
            gap: 30px;
            grid-template-columns: 1fr 1fr;
        }

        .detail-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            padding: 30px;
        }

        .detail-card h2 {
            font-size: 24px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .info-grid {
            display: grid;
            gap: 15px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 500;
            color: #4a5568;
        }

        .info-value {
            font-weight: 600;
            color: #2d3748;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-completed {
            background: #c6f6d5;
            color: #22543d;
        }

        .status-processing {
            background: #fed7aa;
            color: #9c4221;
        }

        .status-failed {
            background: #fed7d7;
            color: #742a2a;
        }

        .donation-status {
            background: #e6fffa;
            color: #234e52;
        }

        .full-width {
            grid-column: span 2;
        }

        .result-content {
            background: #f7fafc;
            border-radius: 10px;
            padding: 20px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #a0aec0;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .detail-grid {
                grid-template-columns: 1fr;
            }

            .full-width {
                grid-column: span 1;
            }

            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-chart-line"></i> 音频分析详情</h1>
            <a href="/audio_history/" class="back-btn">
                <i class="fas fa-arrow-left"></i> 返回历史
            </a>
        </div>

        <!-- Loading -->
        <div id="loading" class="loading">
            <div class="spinner"></div>
            <p>正在加载分析详情...</p>
        </div>

        <!-- Detail Grid -->
        <div id="detail-grid" class="detail-grid" style="display: none;">
            <!-- 动态加载内容 -->
        </div>
    </div>

    <script>
        // API配置
        const API_BASE_URL = '{{ API_BASE_URL }}';
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            const analysisId = getAnalysisIdFromUrl();
            if (analysisId) {
                loadAnalysisDetail(analysisId);
            } else {
                showError('无效的分析ID');
            }
        });

        // 从URL获取分析ID
        function getAnalysisIdFromUrl() {
            const pathParts = window.location.pathname.split('/');
            return pathParts[pathParts.length - 2]; // 假设URL格式为 /audio-detail/{id}/
        }

        // 加载分析详情
        async function loadAnalysisDetail(analysisId) {
            try {
                const token = localStorage.getItem('access_token');
                if (!token) {
                    window.location.href = '/login/';
                    return;
                }

                const response = await fetch(`${API_BASE_URL}/api/audio-analyses/${analysisId}/`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayAnalysisDetail(data.data);
                } else if (response.status === 401) {
                    localStorage.removeItem('access_token');
                    window.location.href = '/login/';
                } else if (response.status === 404) {
                    showError('分析记录不存在');
                } else {
                    showError('加载分析详情失败');
                }
            } catch (error) {
                console.error('Error loading analysis detail:', error);
                showError('网络错误，请稍后重试');
            }
        }

        // 显示分析详情
        function displayAnalysisDetail(data) {
            const analysis = data.analysis;
            const donations = data.donations || [];
            
            document.getElementById('loading').style.display = 'none';
            document.getElementById('detail-grid').style.display = 'grid';
            
            const container = document.getElementById('detail-grid');
            container.innerHTML = `
                <!-- 基本信息 -->
                <div class="detail-card">
                    <h2><i class="fas fa-info-circle"></i> 基本信息</h2>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">分析ID</span>
                            <span class="info-value">#${analysis.id.slice(-8)}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">文件名</span>
                            <span class="info-value">${analysis.filename || '未知'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">上传时间</span>
                            <span class="info-value">${formatDate(analysis.upload_time)}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">分析状态</span>
                            <span class="status-badge ${getStatusClass(analysis.status)}">${getStatusText(analysis.status)}</span>
                        </div>
                    </div>
                </div>

                <!-- 说话人信息 -->
                <div class="detail-card">
                    <h2><i class="fas fa-user"></i> 说话人信息</h2>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">关系</span>
                            <span class="info-value">${analysis.relationship || 'N/A'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">职业</span>
                            <span class="info-value">${analysis.occupation || 'N/A'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">年龄</span>
                            <span class="info-value">${analysis.age || 'N/A'}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">出生日期</span>
                            <span class="info-value">${analysis.date_of_birth || 'N/A'}</span>
                        </div>
                    </div>
                </div>

                <!-- 分析结果 -->
                <div class="detail-card full-width">
                    <h2><i class="fas fa-chart-bar"></i> 分析结果</h2>
                    ${analysis.result ? `
                        <div class="result-content">${analysis.result}</div>
                    ` : `
                        <p style="color: #a0aec0; text-align: center; padding: 40px;">
                            ${analysis.status === 'processing' ? '分析正在进行中，请稍后查看结果...' : '暂无分析结果'}
                        </p>
                    `}
                </div>

                <!-- 捐赠记录 -->
                ${donations.length > 0 ? `
                    <div class="detail-card full-width">
                        <h2><i class="fas fa-heart"></i> 捐赠记录</h2>
                        <div class="info-grid">
                            ${donations.map(donation => `
                                <div class="info-item">
                                    <span class="info-label">捐赠金额: $${donation.amount}</span>
                                    <span class="status-badge donation-status">${getDonationStatusText(donation.status)}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            `;
        }

        // 显示错误信息
        function showError(message) {
            document.getElementById('loading').innerHTML = `
                <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: #e53e3e; margin-bottom: 20px;"></i>
                <h3 style="color: #e53e3e; margin-bottom: 10px;">加载失败</h3>
                <p>${message}</p>
                <a href="/audio_history/" class="back-btn" style="margin-top: 20px; display: inline-block;">
                    <i class="fas fa-arrow-left"></i> 返回历史
                </a>
            `;
        }

        // 格式化日期
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch (status) {
                case 'completed': return 'status-completed';
                case 'processing': return 'status-processing';
                case 'failed': return 'status-failed';
                default: return 'status-processing';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            switch (status) {
                case 'completed': return '已完成';
                case 'processing': return '处理中';
                case 'failed': return '失败';
                default: return '未知';
            }
        }

        // 获取捐赠状态文本
        function getDonationStatusText(status) {
            switch (status) {
                case 'completed': return '已完成';
                case 'pending': return '待处理';
                case 'failed': return '失败';
                case 'refunded': return '已退款';
                default: return '未知';
            }
        }
    </script>
</body>
</html>
