{% extends 'html/base.html' %}
{% load static %}

{% block title %}About Us - HiSage Health{% endblock %}

{% block description %}Learn about HiSage Health's mission to revolutionize dementia screening through advanced speech analysis and AI technology.{% endblock %}

{% block extra_head %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Merriweather:wght@300;400;700&display=swap" rel="stylesheet">
<style>
    :root {
        --primary-blue: #2563eb;
        --primary-blue-dark: #1d4ed8;
        --secondary-blue: #3b82f6;
        --accent-teal: #0d9488;
        --accent-green: #059669;
        --text-dark: #1f2937;
        --text-gray: #6b7280;
        --text-light: #9ca3af;
        --bg-light: #f8fafc;
        --bg-white: #ffffff;
        --border-light: #e5e7eb;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Inter', sans-serif;
        line-height: 1.6;
        color: var(--text-dark);
        background-color: var(--bg-white);
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .section {
        padding: 80px 0;
    }

    .section-title {
        font-size: 2.5rem;
        font-weight: 700;
        text-align: center;
        margin-bottom: 1rem;
        color: var(--text-dark);
    }

    .section-subtitle {
        font-size: 1.25rem;
        text-align: center;
        color: var(--text-gray);
        margin-bottom: 3rem;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Hero Section */
    .hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 120px 0;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
        opacity: 0.3;
    }

    .hero-content {
        position: relative;
        z-index: 1;
    }

    .hero h1 {
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        line-height: 1.2;
    }

    .hero p {
        font-size: 1.25rem;
        margin-bottom: 2rem;
        opacity: 0.9;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .cta-button {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background: var(--accent-teal);
        color: white;
        padding: 1rem 2rem;
        border-radius: 50px;
        text-decoration: none;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: var(--shadow-lg);
        border: none;
        cursor: pointer;
        font-family: inherit;
    }

    .cta-button:hover {
        background: var(--accent-green);
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
        color: white;
        text-decoration: none;
    }

    /* Stats Section */
    .stats {
        background: var(--bg-light);
        padding: 60px 0;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        text-align: center;
    }

    .stat-item {
        background: white;
        padding: 2rem;
        border-radius: 12px;
        box-shadow: var(--shadow-sm);
        transition: all 0.3s ease;
    }

    .stat-item:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-md);
    }

    .stat-number {
        font-size: 3rem;
        font-weight: 700;
        color: var(--primary-blue);
        margin-bottom: 0.5rem;
    }

    .stat-label {
        font-size: 1rem;
        color: var(--text-gray);
        font-weight: 500;
    }

    /* Features Grid */
    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
    }

    .feature-card {
        background: white;
        padding: 2rem;
        border-radius: 16px;
        box-shadow: var(--shadow-md);
        transition: all 0.3s ease;
        border: 1px solid var(--border-light);
    }

    .feature-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1.5rem;
        color: white;
        font-size: 1.5rem;
    }

    .feature-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: var(--text-dark);
    }

    .feature-description {
        color: var(--text-gray);
        line-height: 1.6;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero h1 {
            font-size: 2.5rem;
        }
        
        .hero p {
            font-size: 1.1rem;
        }
        
        .section-title {
            font-size: 2rem;
        }
        
        .section-subtitle {
            font-size: 1.1rem;
        }
        
        .features-grid {
            grid-template-columns: 1fr;
        }
        
        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }
    }
</style>
{% endblock %}

{% block content %}
    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>About HiSage Health</h1>
                <p>Pioneering the future of dementia screening through cutting-edge speech analysis and artificial intelligence technology. We're on a mission to make early detection accessible to everyone.</p>
                <button type="button" onclick="startScreening(); return false;" class="cta-button">
                    <i class="fas fa-microphone"></i>
                    Start Your Journey
                </button>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">55M</div>
                    <div class="stat-label">People with dementia worldwide</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">75%</div>
                    <div class="stat-label">Cases remain undiagnosed</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">95.2%</div>
                    <div class="stat-label">Our AI accuracy rate</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">50K+</div>
                    <div class="stat-label">Lives screened globally</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Our Technology Section -->
    <section class="section" style="background: white;">
        <div class="container">
            <h2 class="section-title">Cookie Theft Picture Description Task</h2>
            <p class="section-subtitle">Understanding the gold standard assessment tool for cognitive evaluation</p>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 4rem; align-items: center; margin-top: 3rem;">
                <div>
                    <div style="background: var(--bg-light); padding: 2rem; border-radius: 16px; text-align: center; margin-bottom: 2rem;">
                        <div style="width: 100%; margin-bottom: 1rem;">
                            <img src="{% static 'images/Cookie-Theft-Picture.png' %}"
                                 alt="Cookie Theft Picture - Standardized neuropsychological assessment image"
                                 style="width: 100%; height: auto; max-height: 400px; object-fit: contain; border-radius: 12px; box-shadow: var(--shadow-md);">
                        </div>
                        <h4 style="color: var(--text-dark); margin-bottom: 1rem; font-size: 1.2rem; font-weight: 600;">Cookie Theft Picture</h4>
                        <p style="color: var(--text-gray); font-size: 1rem; line-height: 1.6;">
                            The Cookie Theft picture from the Boston Diagnostic Aphasia Examination is a standardized assessment tool
                            used in neuropsychological evaluations. Participants describe what they see, revealing important information
                            about their cognitive abilities.
                        </p>
                    </div>
                </div>

                <div>
                    <h3 style="color: var(--text-dark); margin-bottom: 2rem; font-size: 1.8rem;">What the Picture Reveals</h3>
                    <div class="features-grid" style="grid-template-columns: 1fr; gap: 1.5rem;">
                        <div class="feature-card">
                            <div class="feature-icon" style="background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));">
                                <i class="fas fa-comments"></i>
                            </div>
                            <h5 class="feature-title">Language Production</h5>
                            <p class="feature-description">
                                Evaluates fluency, word-finding ability, grammatical complexity, and semantic content.
                                Detects subtle language changes that may indicate cognitive decline.
                            </p>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon" style="background: linear-gradient(135deg, var(--accent-teal), var(--accent-green));">
                                <i class="fas fa-eye"></i>
                            </div>
                            <h5 class="feature-title">Visual Processing</h5>
                            <p class="feature-description">
                                Assesses ability to perceive, interpret, and describe visual information. Tests spatial
                                awareness and visual-cognitive integration.
                            </p>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon" style="background: linear-gradient(135deg, #7c3aed, #8b5cf6);">
                                <i class="fas fa-brain"></i>
                            </div>
                            <h5 class="feature-title">Executive Function</h5>
                            <p class="feature-description">
                                Measures planning, organization, and the ability to structure a coherent narrative.
                                Reveals problems with sequencing and logical thinking.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI Analysis Components -->
            <div style="margin-top: 4rem; background: var(--bg-light); padding: 3rem; border-radius: 20px;">
                <h3 style="color: var(--text-dark); text-align: center; margin-bottom: 3rem; font-size: 2rem;">AI Analysis Components</h3>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-waveform-lines"></i>
                        </div>
                        <h5 class="feature-title">Acoustic Features</h5>
                        <p class="feature-description">Pitch, rhythm, pause patterns, voice quality, and speech timing analysis</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-spell-check"></i>
                        </div>
                        <h5 class="feature-title">Linguistic Analysis</h5>
                        <p class="feature-description">Vocabulary complexity, grammar usage, semantic coherence, and word choice patterns</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-stopwatch"></i>
                        </div>
                        <h5 class="feature-title">Temporal Patterns</h5>
                        <p class="feature-description">Speech rate variations, hesitations, response latency, and fluency measures</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <h5 class="feature-title">Discourse Structure</h5>
                        <p class="feature-description">Narrative organization, topic maintenance, and coherence analysis</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Early Detection Matters Section -->
    <section class="section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; position: relative; overflow: hidden;">
        <!-- Background Pattern -->
        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0;
                    background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1000 1000\"><defs><radialGradient id=\"a\" cx=\"50%\" cy=\"50%\"><stop offset=\"0%\" stop-color=\"%23ffffff\" stop-opacity=\"0.1\"/><stop offset=\"100%\" stop-color=\"%23ffffff\" stop-opacity=\"0\"/></radialGradient></defs><circle cx=\"200\" cy=\"200\" r=\"100\" fill=\"url(%23a)\"/><circle cx=\"800\" cy=\"300\" r=\"150\" fill=\"url(%23a)\"/><circle cx=\"400\" cy=\"700\" r=\"120\" fill=\"url(%23a)\"/></svg>');
                    opacity: 0.3;"></div>

        <div class="container" style="position: relative; z-index: 2;">
            <div style="text-align: center; margin-bottom: 4rem;">
                <h2 class="section-title" style="color: white; font-size: 3rem; margin-bottom: 1rem;">
                    <i class="fas fa-clock" style="margin-right: 1rem; opacity: 0.9;"></i>
                    Why Early Detection Matters?
                </h2>
                <p class="section-subtitle" style="color: white; opacity: 0.9; font-size: 1.3rem; max-width: 800px;">
                    The window of opportunity for intervention is largest in the earliest stages of cognitive decline.
                    <strong>Every moment counts</strong> when it comes to preserving cognitive health.
                </p>
            </div>

            <!-- Key Benefits Grid -->
            <div class="features-grid" style="margin-bottom: 4rem;">
                <div class="feature-card" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2); color: white;">
                    <div class="feature-icon" style="background: rgba(255,255,255,0.2); color: white;">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3 class="feature-title" style="color: white;">Preserve Cognitive Function</h3>
                    <p class="feature-description" style="color: rgba(255,255,255,0.9);">
                        Early intervention can slow cognitive decline by up to <strong>30%</strong>, preserving independence
                        and quality of life for years longer. Research shows that lifestyle changes and medical interventions
                        are most effective when implemented early.
                    </p>
                    <div style="margin-top: 1rem; padding: 0.5rem 1rem; background: rgba(255,255,255,0.2); border-radius: 20px; display: inline-block;">
                        <span style="font-weight: 600; font-size: 0.9rem;">Up to 30% slower decline</span>
                    </div>
                </div>

                <div class="feature-card" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2); color: white;">
                    <div class="feature-icon" style="background: rgba(255,255,255,0.2); color: white;">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3 class="feature-title" style="color: white;">Reduce Family Burden</h3>
                    <p class="feature-description" style="color: rgba(255,255,255,0.9);">
                        Families have more time to plan, access resources, and make informed decisions about care and treatment options.
                        Early detection provides the gift of time to create meaningful memories and prepare for the future.
                    </p>
                    <div style="margin-top: 1rem; padding: 0.5rem 1rem; background: rgba(255,255,255,0.2); border-radius: 20px; display: inline-block;">
                        <span style="font-weight: 600; font-size: 0.9rem;">Better family planning</span>
                    </div>
                </div>

                <div class="feature-card" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2); color: white;">
                    <div class="feature-icon" style="background: rgba(255,255,255,0.2); color: white;">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <h3 class="feature-title" style="color: white;">Lower Healthcare Costs</h3>
                    <p class="feature-description" style="color: rgba(255,255,255,0.9);">
                        Early detection can reduce healthcare costs by up to <strong>50%</strong> through preventive care
                        and delayed institutionalization. Prevention is always more cost-effective than treatment.
                    </p>
                    <div style="margin-top: 1rem; padding: 0.5rem 1rem; background: rgba(255,255,255,0.2); border-radius: 20px; display: inline-block;">
                        <span style="font-weight: 600; font-size: 0.9rem;">50% cost reduction</span>
                    </div>
                </div>
            </div>

            <!-- Critical Window Section -->
            <div style="background: rgba(255,255,255,0.1); padding: 3rem; border-radius: 20px; text-align: center;
                        backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2); margin-bottom: 3rem;">
                <div style="margin-bottom: 2rem;">
                    <i class="fas fa-lightbulb" style="font-size: 3rem; color: white; opacity: 0.9;"></i>
                </div>
                <h3 style="color: white; margin-bottom: 2rem; font-size: 2rem; font-weight: 600;">
                    The Critical 15-20 Year Window
                </h3>
                <p style="color: rgba(255,255,255,0.95); line-height: 1.8; font-size: 1.2rem; max-width: 800px; margin: 0 auto 2rem;">
                    Research shows that cognitive changes begin <strong>15-20 years</strong> before clinical symptoms appear.
                    This pre-symptomatic phase represents our greatest opportunity for intervention, lifestyle modifications,
                    and treatment that can significantly alter the disease trajectory.
                </p>

                <!-- Timeline Stats -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 2rem; margin-top: 2rem;">
                    <div>
                        <div style="font-size: 2.5rem; font-weight: 700; color: white; margin-bottom: 0.5rem;">15-20</div>
                        <div style="font-size: 1rem; opacity: 0.9;">Years before symptoms</div>
                    </div>
                    <div>
                        <div style="font-size: 2.5rem; font-weight: 700; color: white; margin-bottom: 0.5rem;">85%</div>
                        <div style="font-size: 1rem; opacity: 0.9;">Treatment effectiveness</div>
                    </div>
                    <div>
                        <div style="font-size: 2.5rem; font-weight: 700; color: white; margin-bottom: 0.5rem;">5-10</div>
                        <div style="font-size: 1rem; opacity: 0.9;">Years gained with early care</div>
                    </div>
                </div>
            </div>

            <!-- Call to Action -->
            <div style="text-align: center;">
                <p style="font-size: 1.2rem; opacity: 0.9; margin-bottom: 2rem; color: white;">
                    Don't wait for symptoms to appear. Take control of your cognitive health today.
                </p>
                <button type="button" onclick="startScreening(); return false;"
                        style="background: white; color: var(--primary-blue); padding: 1.2rem 2.5rem;
                               border: none; border-radius: 50px; font-weight: 600; font-size: 1.2rem;
                               cursor: pointer; transition: all 0.3s ease; display: inline-flex;
                               align-items: center; gap: 0.8rem; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
                    <i class="fas fa-microphone"></i>
                    Start Your Screening Now
                </button>
            </div>
        </div>
    </section>

    <!-- Mission & Vision Section -->
    <section class="section" style="background: var(--bg-light);">
        <div class="container">
            <h2 class="section-title">Our Mission & Vision</h2>
            <p class="section-subtitle">Transforming early dementia detection through innovative AI-powered speech analysis</p>

            <div class="features-grid" style="margin-top: 3rem;">
                <div class="feature-card" style="text-align: center;">
                    <div class="feature-icon" style="background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue)); margin: 0 auto;">
                        <i class="fas fa-target"></i>
                    </div>
                    <h3 class="feature-title">Our Mission</h3>
                    <p class="feature-description">
                        To democratize early dementia detection through cutting-edge AI technology, making cognitive health
                        screening accessible, accurate, and affordable for everyone worldwide.
                    </p>
                </div>

                <div class="feature-card" style="text-align: center;">
                    <div class="feature-icon" style="background: linear-gradient(135deg, var(--accent-teal), var(--accent-green)); margin: 0 auto;">
                        <i class="fas fa-eye"></i>
                    </div>
                    <h3 class="feature-title">Our Vision</h3>
                    <p class="feature-description">
                        A world where cognitive decline is detected years before symptoms appear, enabling timely intervention
                        and preserving quality of life for millions of families.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="section" style="background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue)); color: white; text-align: center;">
        <div class="container">
            <h2 style="color: white; margin-bottom: 1rem; font-size: 2.5rem; font-weight: 700;">
                <i class="fas fa-rocket" style="margin-right: 1rem; opacity: 0.9;"></i>
                Ready to Transform Cognitive Healthcare?
            </h2>
            <p style="font-size: 1.2rem; opacity: 0.9; margin-bottom: 2rem; max-width: 600px; margin-left: auto; margin-right: auto; line-height: 1.6;">
                Join thousands of healthcare providers and individuals who trust HiSage Health for early dementia detection.
            </p>
            <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                <button type="button" onclick="startScreening(); return false;" class="cta-button" style="background: white; color: var(--primary-blue);">
                    <i class="fas fa-microphone"></i>
                    Start Screening Now
                </button>
                <a href="/contact/" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 1rem 2rem;
                                          background: rgba(255,255,255,0.1); color: white; text-decoration: none; border-radius: 50px;
                                          font-weight: 600; transition: all 0.3s ease; backdrop-filter: blur(10px);
                                          border: 1px solid rgba(255,255,255,0.2);">
                    <i class="fas fa-envelope"></i>
                    Contact Our Team
                </a>
            </div>
        </div>
    </section>

<script>
    // Function to check if user is authenticated
    function isUserAuthenticated() {
        const token = localStorage.getItem('access_token');
        if (!token) {
            console.log('❌ No access token found');
            return false;
        }

        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const currentTime = Date.now() / 1000;

            if (payload.exp < currentTime) {
                console.log('❌ Token expired');
                localStorage.removeItem('access_token');
                localStorage.removeItem('refresh_token');
                return false;
            }

            console.log('✅ User is authenticated');
            return true;
        } catch (error) {
            console.error('❌ Error checking token:', error);
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            return false;
        }
    }

    // Function to start screening
    function startScreening() {
        console.log('🎯 Starting screening process...');

        const isAuth = isUserAuthenticated();
        console.log('🔍 Authentication result:', isAuth);

        if (isAuth) {
            console.log('✅ User is authenticated, redirecting to audio upload');
            window.location.href = '/audio_upload/';
        } else {
            console.log('⚠️ User not authenticated, redirecting to login');
            // Store the intended destination
            sessionStorage.setItem('redirectAfterLogin', '/audio_upload/');
            console.log('💾 Stored redirect URL in sessionStorage');
            window.location.href = '/login/';
        }
    }

    // Make function available globally
    window.startScreening = startScreening;

    // Ensure function is available when page loads
    document.addEventListener('DOMContentLoaded', () => {
        window.startScreening = startScreening;
        console.log('✅ About page: startScreening function loaded');
    });
</script>

{% endblock %}
