
from django.conf import settings

from django.shortcuts import redirect
from django.conf.urls.static import static
from django.contrib.sitemaps.views import sitemap

from django.views.generic import TemplateView

from django.contrib import admin
from django.urls import path

from django.urls import path, include, re_path

from .views import (rate_limiter_view, view_404,
                        handler_403, home_view, about_view, blogs_view, auth_test_view, register_page, login_page, verify_code_page) #subscribe_view

from .sitemaps import StaticSitemap
# from blog.sitemaps import BlogSitemap

handler404 = view_404

handler403 = handler_403

admin.site.site_header = 'Admin panel'           
admin.site.index_title = 'Site Admin'              
admin.site.site_title = 'Admin site'
admin.site.site_url = "" 


# sitemap_dict = {'sitemaps': {'static': StaticSitemap, 'blog': BlogSitemap}}

from .views import (register_page, login_page, password_reset_page, password_reset_confirm_page,
                   profile_page, settings_page, audio_history_page, audio_detail_page, test_new_features_page)

urlpatterns = [
    path('admin/', admin.site.urls),

    path('user/', include('user.urls')),
    path('blog/', include('blog.urls')),
    path('contact-us/', include('inquiry.urls')),

    # path('sitemap.xml', sitemap, sitemap_dict, name='django.contrib.sitemaps.views.sitemap'),
    path('robots.txt', TemplateView.as_view(template_name="robots.txt", content_type='text/plain')),
    path('ratelimit-error/', rate_limiter_view, name='ratelimit-error'),

    # add new path here

    path('', home_view, name='home'),
    path('about/', about_view, name='about'),
    path('blogs/', blogs_view, name='blogs'),
    path('auth-test/', auth_test_view, name='auth_test'),

    path("__reload__/", include("django_browser_reload.urls")),

    path('audio_upload/', include('audio_upload.urls')),

    # API endpoints handled by separate backend service
    # path('api/', include('api_urls')),

    path('register/', register_page, name='register'),
    path('login/', login_page, name='login'),
    path('verify-code/', verify_code_page, name='verify_code'),

    # 密码重置页面
    path('password-reset/', password_reset_page, name='password_reset'),
    path('password-reset-confirm/', password_reset_confirm_page, name='password_reset_confirm'),

    # 用户页面
    path('profile/', profile_page, name='profile'),
    path('settings/', settings_page, name='settings'),
    path('audio-history/', audio_history_page, name='audio_history'),
    path('audio-detail/<uuid:analysis_id>/', audio_detail_page, name='audio_detail'),

    # API测试页面
    path('test-api/', TemplateView.as_view(template_name='../test_api.html'), name='test_api'),
    path('test-new-features/', test_new_features_page, name='test_new_features'),
    path('test-register/', TemplateView.as_view(template_name='html/test_register.html'), name='test_register'),

]

if settings.DEBUG:
   urlpatterns +=  []

urlpatterns += static(settings.MEDIA_URL, document_root = settings.MEDIA_ROOT)
urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
   
urlpatterns += [ re_path(r'^.*/$', view_404, name='page_not_found'),]